from playwright.sync_api import Page
from sugon_web.common.elements import CommonPage
from sugon_web.common.logger import logger


class Base:

    def __init__(self, page: Page, env: dict):
        self.page = page
        self.env = env  # 接收环境配置
        self.logger = logger  # 传递日志工具

    @property
    def base_url(self):
        """动态获取当前环境的base_url"""
        return self.env["url"]

    def locator(self, selector):
        """根据选择器定位页面元素"""
        return self.page.locator(selector)

    def get_by_test_id(self, test_id):
        """通过测试ID定位元素"""
        return self.page.get_by_test_id(test_id)

    def get_by_role(self, role, name):
        """通过角色和名称定位元素"""
        return self.page.get_by_role(role, name=name)

    def get_by_placeholder(self, text):
        """通过占位符定位元素"""
        return self.page.get_by_placeholder(text)

    def get_by_label(self, text):
        """通过标签文本定位元素"""
        return self.page.get_by_label(text)

    def get_by_text(self, text):
        """通过文本内容定位元素"""
        return self.page.get_by_text(text)

    def get_by_alt_text(self, text):
        """通过ALT文本定位元素"""
        return self.page.get_by_alt_text(text)

    def get_by_title(self, text):
        """通过标题定位元素"""
        return self.page.get_by_title(text)

    def click(self, selector):
        """点击元素，自动处理文本选择器的引号"""
        # 如果已经有引号，保持原样
        if selector.startswith(('"', "'")):
            return self.page.click(selector)

        # 如果是明显的CSS选择器，保持原样
        if selector.startswith(('#', '.', '[', '(')) or '//' in selector:
            return self.page.click(selector)

        # 其他情况当作文本处理，添加双引号
        selector = f'"{selector}"'
        return self.page.click(selector)

    def fill(self, selector, value):
        """输入文本"""
        return self.page.fill(selector, value)

    def hover(self, selector):
        """悬停元素"""
        # 如果已经有引号，保持原样
        if selector.startswith(('"', "'")):
            return self.page.hover(selector)

        # 如果是明显的CSS选择器，保持原样
        if selector.startswith(('#', '.', '[', '(')) or '//' in selector:
            return self.page.hover(selector)

        # 其他情况当作文本处理，添加双引号
        selector = f'"{selector}"'
        return self.page.hover(selector)

    def pop_message(self):
        """页面弹窗元素"""
        return self.locator(CommonPage.message)

    # 公共对话框按钮操作
    def click_confirm(self, timeout=5000):
        """点击确定按钮"""
        try:
            self.get_by_role("button", "确定").click(timeout=timeout)
            return True
        except Exception as e:
            self.logger.warning(f"点击确定按钮失败: {e}")
            return False

    def click_cancel(self, timeout=5000):
        """点击取消按钮"""
        try:
            self.get_by_role("button", "取消").click(timeout=timeout)
            return True
        except Exception as e:
            self.logger.warning(f"点击取消按钮失败: {e}")
            return False

    def close_dialog_if_exists(self, timeout=5000):
        """如果存在对话框，则关闭它"""
        if self.click_confirm(timeout):
            return True
        elif self.click_cancel(timeout):
            return True
        return False

    def wait_for_dialog_and_confirm(self, timeout=10000):
        """等待对话框出现并点击确定"""
        try:
            # 等待确定按钮出现
            confirm_btn = self.get_by_role("button", "确定")
            confirm_btn.wait_for(state="visible", timeout=timeout)
            confirm_btn.click()
            return True
        except Exception as e:
            self.logger.warning(f"等待对话框并确定失败: {e}")
            return False

    def search(self, keyword: str):
        """执行搜索操作"""
        self.locator(CommonPage.ipt_search).fill(keyword)
        self.locator(CommonPage.btn_search).click()

    def navigate_to_service(self, category: str, service: str, menu: str):
        """通用服务导航方法"""
        self.hover('资源中心')
        self.hover(category)
        self.click(service)
        self.click(menu)