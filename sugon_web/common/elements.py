"""
NavMenu类只存放导航菜单、设置菜单的元素
MainPage类仅存放页面中的所有公共元素，比如alert、dialog、notification

定位元素的变量命名前缀规则如下：
按钮：btn_*
输入框：ipt_*
下拉框：drop_*
选择框：select_*
单选框：radio_*
"""


class CommonPage:
    message = ".el-message__content"  # 页面弹窗
    ipt_search = ".input-with-select > .el-input__inner"  # 页面列表搜索框
    btn_search = '"搜索"'  # 搜索按钮


class CreatePage:
    drop_version = '(//input[@type="text"])[6]'   # CCE版本下拉框
    drop_az = '(//input[@type="text"])[7]'   # 集群下拉框
    drop_net_type = '.el-form-item:nth-child(2) .el-select__caret'   # CCE容器网络模型下拉框
    drop_disk_type = '(//input[@type="text"])[29]'   # 云硬盘类型下拉框
    ipt_disk_size = '(//input[@type="text"])[30]'   # 云硬盘大小下拉框
    drop_os_type = '(//input[@type="text"])[31]'   # 操作系统下拉框
    radio_flavor = '.el-table__fixed-body-wrapper .el-table__row:nth-child(1) .el-radio__inner'  # 规格单选框






